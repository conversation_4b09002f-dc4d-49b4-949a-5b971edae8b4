<template>
  <Popup 
    :show="show" 
    :popup-title="popupTitle" 
    :is-use-ele="true" 
    :loading="loading" 
    class="add-agent-popup"
    @cancel="cancel" 
    @submit="submitForm"
  >
    <div slot="popupContent" class="add-agent-box custom-form">
      <el-form ref="addAgentRuleForm" :model="newAgentObj" :rules="rules">
        <!-- 代理商 -->
        <el-form-item :label="$t('agent.agentName')" prop="agentName" class="add-agent-label">
          <CRMList :searchName="searchNameForCRM" @update="setBasicInfo">
            <hg-input 
              v-model="newAgentObj.agentName" 
              :placeholder="$t('agent.agentNamePlaceholder')" 
              slot="reference"
              @input="handleAgentNameInput"
            />
          </CRMList>
        </el-form-item>
        
        <!-- 编码 -->
        <el-form-item :label="$t('agent.agentCode')" prop="agentCode" class="add-agent-label">
          <el-input v-model="newAgentObj.agentCode" :disabled="disableCode" type="text" :placeholder="$t('customer.orgSnPlaceholder')" />
        </el-form-item>
        
        <!-- 代理范围 -->
        <el-form-item :label="$t('agent.agentScope')" prop="rangeType" class="add-agent-label">
          <div class="input-box">
            <Select 
              :select-options="agentScopeList" 
              :value="newAgentObj.rangeType" 
              :isMultiple="true"
              :placeholder="$t('agent.agentScopePlaceholder')" 
              @change="changeRangeType" 
            />
          </div>
          <div class="warning-tip"><span class="info"><i class="el-icon-info"></i> {{$t('agent.agentScopeWarning')}}</span></div>
        </el-form-item>

        <!-- 币种 -->
        <el-form-item :label="$t('agent.currency')" prop="currency" class="add-agent-label">
          <div class="input-box">
            <Select :select-options="currencyList" :value="newAgentObj.currency"  @change="changeCurrency" />
          </div>
        </el-form-item>

         <!-- 邮箱 -->
        <el-form-item :label="$t('agent.email')" prop="email" class="add-agent-label">
          <el-input v-model="newAgentObj.email" type="text" :placeholder="$t('customer.emailPlaceholder')" />
        </el-form-item>

       <!-- 区域 -->
        <el-form-item :label="$t('customer.area')" prop="areaCode" class="add-agent-label">
          <div class="input-box">
            <el-cascader style="width: 100%;" v-model="newAgentObj.areaCode" :props="{label: 'label', value: 'areaCode'}" :options="areaList" @change="changeArea"></el-cascader>
          </div>
        </el-form-item>
        
        <!-- 公司地址 -->
        <el-form-item :label="$t('agent.address')" class="add-agent-label">
          <el-input v-model="newAgentObj.address" type="text"  :placeholder="$t('customer.addressPlaceholder')" />
        </el-form-item>
        
        <!-- 负责人 -->
        <el-form-item :label="$t('agent.leader')" class="add-agent-label">
          <el-input v-model="newAgentObj.leader" type="text" :placeholder="$t('customer.leaderPlaceholder')" />
        </el-form-item>
        
        <!-- 电话 -->
        <el-form-item :label="$t('agent.mobile')" prop="mobile" class="add-agent-label">
          <div class="area-code">
            <Select :select-options="countryListArrayComputed" :value="newAgentObj.mobilePrefix" :placeholder="$t('agent.areaCode')" @change="changeAreaCode" />
          </div>
          <el-input v-model="newAgentObj.mobile" type="text" :placeholder="$t('customer.phonePlaceholder')" />
        </el-form-item>
        

        <!-- 关联业务人员 -->
        <el-form-item :label="$t('agent.businessUser')" class="add-agent-label">
          <div class="input-box">
            <Select :placeholder="$t('customer.salemanPlaceholder')" :select-options="businessUserList" :value="newAgentObj.businessUserCodes" :is-multiple="false" @focus="showBusinessUserList" @change="changeBusinessUser" />
          </div>
        </el-form-item>
        
        <!-- 技术支持 -->
        <el-form-item :label="$t('agent.techSupport')" prop="techSupport" class="add-agent-label">
          <div class="input-box">
            <Select 
              :select-options="techSupportList" 
              :value="newAgentObj.techSupport" 
              :isMultiple="true"
              @change="changeTechSupport" 
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
  </Popup>
</template>

<script>
import Popup from '@/components/func-components/Popup'
import Select from '@/components/func-components/Select'
import { COMMON_CONSTANTS } from '@/assets/script/constants.js'
import { refreshLabel } from '@/assets/script/refreshLabel.js'
import { getTimezoneList, getBusinessUserList,getAreaList} from '@/api/common'
import { getAllCurrency } from '@/api/customer'
import { getStore } from '@/assets/script/storage.js'
import CRMList from '../Customer/CRMList';
import HgInput from '@/components/func-components/HgInput'

export default {
  name: 'AddAgent',
  components: {
    Popup,
    Select,
    CRMList,
    HgInput
  },
  props: {
    popupTitle: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    areaCodeArr: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    var checkMobile = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.PHONE_RULE.test(value)) {
          return callback(new Error(this.$t('agent.mobileFormatError')))
        }
      }
      callback()
    }
    var checkEmail = (rule, value, callback) => {
      if (value) {
        if (!COMMON_CONSTANTS.EMAIL_RULE.test(value)) {
          return callback(new Error(this.$t('agent.emailFormatError')))
        }
      }
      callback()
    }
    
    return {
      newAgentObj: {
        agentName: '',
        agentCode: '',
        rangeType: [],
        currency: '',
        email: '',
        tzCode: getStore('userInfo').timezone.tzCode,
        address: '',
        leader: '',
        mobile: '',
        mobilePrefix: '+86',
        businessUserCodes: null, // 关联业务人员
        techSupport: []
      },
      searchNameForCRM: null, // 初始值设为null而不是空字符串
      rules: {
        agentName: [
          { required: true, message: this.$t('agent.agentNameRequired') }
        ],
        agentCode: [
          { required: true, message: this.$t('agent.agentCodeRequired') }
        ],
        rangeType: [
          { required: true, message: this.$t('agent.agentScopeRequired'), type: 'array', min: 1 }
        ],
        currency: [
          { required: true, message: this.$t('agent.currencyRequired') }
        ],
        email: [
          { required: true, message: this.$t('agent.emailRequired') },
          { validator: checkEmail, trigger: 'blur' }
        ],
        // tzCode: [
        //   { required: true, message: this.$t('agent.timezoneRequired') }
        // ],
        mobile: [
          { validator: checkMobile, trigger: 'blur' }
        ],
        businessUser: [
          { required: true, message: this.$t('agent.businessUserRequired') }
        ],
         areaCode: [
          { required: true, message: this.$t('customer.selectArea') }
        ],
        // techSupport: [
        //   { required: true, message: this.$t('agent.techSupportRequired'), type: 'array', min: 1 }
        // ]
      },
     // 代理范围列表
      agentScopeList: [
        { value: 0, label: this.$t('agent.deviceAgent') },
        // { value: 1, label: this.$t('agent.designServiceAgent'), disabled: true },
        // { value: 2, label: this.$t('agent.aiSoftwareAgent'), disabled: true }
      ],
      // 技术支持列表
      techSupportList: [],
      // 其他列表保持不变
      timezoneList: [],
      areaList: [],
      businessUserList: [],
      currencyList: [],
      loading: false,
      disableCode: false
    }
  },
  computed: {
    countryListArrayComputed() { // 根据目前的中英文状态返回相对应的中英文区号
      const countryListArrayNew = []
      this.areaCodeArr.forEach((item) => {
        if (this.$i18n.locale === 'zh') {
          item.label = item.countryName + ' +' + item.mobilePrefix
        } else {
          item.label = item.countryEn + ' +' + item.mobilePrefix
        }
        item.value = item.mobilePrefix
        countryListArrayNew.push(item)
      })
      return countryListArrayNew
    }
  },
  watch: {
    show(val) {
      if (val) {
        refreshLabel('add-agent-label')
        Promise.allSettled([
          this.getTimezoneListFunc(), 
          this.getAllCurrencyFunc(), 
          this.getBusinessUserListFunc(),
          this.getTechSupportListFunc(),
          this.getAreaList()
        ])
      } else {
        this.resetForm('addAgentRuleForm')
        this.disableCode = false
      }
    },
    // 监听代理商名称变化，控制CRM搜索
    'newAgentObj.agentName'(newVal, oldVal) {
      // 如果新值为空或只包含空格，不触发搜索
      if (!newVal || newVal.trim() === '') {
        // 可以在这里清空相关的搜索结果或状态
        return;
      }
      // 如果有内容且与旧值不同，可以触发搜索逻辑
      if (newVal.trim() !== (oldVal || '').trim()) {
        console.log('触发CRM搜索:', newVal.trim());
      }
    }
  },
  mounted() {
    // this.$on('isLoading', () => {
    //   this.loading = false
    // })
  },
  methods: {
    cancel() {
      this.loading = false
      this.$emit('update:show', false)
    },
    setBasicInfo(info) {
      this.newAgentObj.agentCode = info.orgSn;
      this.newAgentObj.agentName = info.orgName;
      this.newAgentObj.address = info.orgAddress;
      this.disableCode = true;
    },
    submitForm() {
      this.$refs['addAgentRuleForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          let data = Object.assign({}, this.newAgentObj);
          if(this.disableCode) {
            data.isBackupCrmOrg = 1;
          }
          this.$emit('submit', data)
        } else {
          this.loading = false
          return false
        }
      })
    },
    // 选择代理范围
    changeRangeType(value) {
      this.newAgentObj.rangeType = value
    },
    // 选择币种
    changeCurrency(value) {
      this.newAgentObj.currency = value
    },
    // 获取区域列表
    async getAreaList(){
      const loop = (arr) => {
        arr.forEach((item) => {
          item.label =  this.$i18n.locale === 'zh' ? item.nameCn : item.name;
          if(item.children){
            loop(item.children)
          }
        })
        return arr
      }
      const { code, data } = await getAreaList();
      if(code == 200){
        this.areaList = loop(data);
        console.log(this.areaList)
      }
    },
    // 选择时区
    changeTimezone(value) {
      this.newAgentObj.tzCode = value
    },
    // 选择区域
    changeArea(value){
      this.newAgentObj.areaCode = value
    },
    // 选择区号
    changeAreaCode(value) {
      this.newAgentObj.mobilePrefix = '+' + Number(value)
    },
    // 选择业务员
    changeBusinessUser(value) {
      this.newAgentObj.businessUserCodes = value
    },
    // 选择技术支持
    changeTechSupport(value) {
      this.newAgentObj.techSupport = value
    },
    // 打开业务人员下拉框
    showBusinessUserList() {
      //this.getBusinessUserListFunc()
    },
    // 获取币种列表
    getAllCurrencyFunc() {
      getAllCurrency().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.currencyList = res.data
            this.currencyList.forEach((item) => {
              item.label = item.currency
              item.lable_en = item.currencyEn
              item.value = item.settlementCurrency
            })
          }
        }
      })
    },
    // 获取业务人员列表
    getBusinessUserListFunc() {
      getBusinessUserList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.businessUserList = res.data
            this.businessUserList.forEach((item) => {
              item.label = item.realName
              item.value = item.userCode
            })
          }
        }
      })
    },
    
    // 获取技术支持列表
    getTechSupportListFunc() {
      // 技术支持列表通常与业务人员列表相同，或者需要单独的API
      getBusinessUserList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.techSupportList = res.data
            this.techSupportList.forEach((item) => {
              item.label = item.realName
              item.value = item.userCode
            })
          }
        }
      })
    },
    
    // 获取时区信息列表
    getTimezoneListFunc() {
      getTimezoneList().then((res) => {
        if (res.code === 200) {
          if (res.data != null && res.data.length) {
            this.timezoneList = res.data
            this.timezoneList.forEach((item) => {
              item.label = this.$i18n.locale === 'zh' ? item.tzNameCn : item.tzNameEn;
              item.value = item.tzCode
            })
          }
        }
      })
    },
    
    // 重置表单
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields()
      }
      this.newAgentObj = {
        agentName: '',
        agentCode: '',
        rangeType: [],
        currency: '',
        email: '',
        tzCode: getStore('userInfo').timezone.tzCode,
        areaCode: '',
        address: '',
        leader: '',
        mobile: '',
        mobilePrefix: '+86',
        businessUserCodes: [],
        techSupport: []
      }
    },
    // 处理代理商名称输入
    handleAgentNameInput(value) {
      // 只有当输入内容非空且长度大于0时才传递给CRM搜索
      if (value && value.trim() !== '' && value.trim().length > 0) {
        this.searchNameForCRM = value.trim();
      } else {
        this.searchNameForCRM = null; // 使用null而不是空字符串
      }
    },
    // 处理输入框失焦事件
    handleInputBlur() {
      // 如果输入框内容为空，确保searchNameForCRM为null
      if (!this.newAgentObj.agentName || this.newAgentObj.agentName.trim() === '') {
        this.searchNameForCRM = null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-agent-box {
  .input-box {
    width: 320px;
  }
  .area-code {
    width: 96px;
    margin-right: 12px;
  }
  .warning-tip {
    color: #FBAA0E;
    font-size: 14px;
    margin-top: 4px;
    text-align: right; // 左右对齐
    height: 20px;
    line-height: 20px;
    font-weight: 400;
    letter-spacing: 0%;
    opacity: 1;
    display: flex;
    align-items: center; // 垂直居中对齐
    justify-content: flex-end; // 水平右对齐
  }
  
  // 统一表单项样式
  .add-agent-label {
    // label右对齐
    .el-form-item__label {
      text-align: right !important;
      padding-right: 12px !important;
      width: 94px !important; // 根据图片设置固定宽度
      line-height: 40px !important; // 与输入框高度对齐
    }
    
    .el-form-item__content {
      margin-left: 94px !important; // 与label宽度对应
      
      // 普通输入框样式
      .el-input {
        width: 320px;
        
        .el-input__inner {
          width: 320px;
          height: 40px;
          border-radius: 4px;
          border-width: 1px;
          padding: 10px 24px;
          opacity: 1;
        }
      }
      
      // hg-input组件样式
      .hg-input {
        width: 320px;
        
        input {
          width: 320px;
          height: 40px;
          border-radius: 4px;
          border-width: 1px;
          padding: 10px 24px;
          opacity: 1;
        }
      }
      
      // 下拉框容器样式
      .input-box {
        width: 320px;
        
        // Select组件样式
        .select-component {
          width: 320px;
          height: 40px;
        }
      }
      
      // 联系电话特殊处理 - 区号下拉框
      .area-code {
        width: 96px;
        margin-right: 12px;
        display: inline-block;
        vertical-align: top;
        
        .select-component {
          width: 96px;
          height: 40px;
        }
      }
      
      // 联系电话输入框调整宽度
      &:has(.area-code) {
        .el-input {
          width: calc(320px - 96px - 12px); // 总宽度减去区号宽度和间距
          display: inline-block;
          vertical-align: top;
          
          .el-input__inner {
            width: calc(320px - 96px - 12px);
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.custom-form .el-form .el-form-item .el-form-item__content .el-input .el-input__suffix{
  right: 10px;
}

// 添加代理商弹窗样式 - 520x805尺寸
.add-agent-popup {
  .el-dialog {
    width: 520px !important;
    height: 805px !important;
    margin-top: calc(50vh - 402.5px) !important; // 垂直居中 (805/2 = 402.5)
    margin-bottom: 0 !important;
    
    .el-dialog__header {
      padding: 20px 24px 16px 24px;
      border-bottom: 1px solid #f0f0f0;
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      height: calc(805px - 130px); // 减去头部和底部的高度
      overflow-y: auto;
    }
    
    .el-dialog__footer {
      padding: 16px 24px 20px 24px;
      border-top: 1px solid #f0f0f0;
      text-align: right;
      
      .el-button {
        min-width: 80px;
        height: 36px;
        font-size: 14px;
        
        &:not(:last-child) {
          margin-right: 12px;
        }
      }
    }
  }
  
  // 确保Select组件在弹窗中的样式统一
  .select-component {
    width: 320px !important;
    height: 40px !important;
    
    .el-input {
      width: 320px !important;
      
      .el-input__inner {
        height: 40px !important;
        line-height: 40px !important;
      }
    }
  }
  
  // 区号下拉框特殊处理
  .area-code .select-component {
    width: 96px !important;
    
    .el-input {
      width: 96px !important;
    }
  }
}
</style>

  .agent-scope-form {
    width: 320px;
    padding: 12px 0;
  }
