<template>
  <div v-if="show" class="agent-account-box">
    <!-- 头部导航栏 -->
    <div class="header">
      <p class="back-btn border finger" @click="goBack">
        <i class="iconfont icon-arrow-back" />{{ $t('agent.back') }}
      </p>
      <p class="header-title">{{ $t('agent.accountTitle') }}<em>{{ agentInfo.agentName || $t('agent.unnamedAgent') }}</em></p>
    </div>

    <!-- 自定义选项卡导航 -->
    <div class="header-tab">
      <span :class="['tab-item', activeTab === 'a2d_hd_4k' ? 'active' : '']" @click="changeTab('a2d_hd_4k')">A2D HD 4K</span>
      <!-- 预留其他选项卡 -->
      <!-- <span :class="['tab-item', activeTab === 'design_service' ? 'active' : '']" @click="changeTab('design_service')">设计服务</span>
      <span :class="['tab-item', activeTab === 'ai_software' ? 'active' : '']" @click="changeTab('ai_software')">AI软件</span> -->
    </div>

    <!-- 选项卡内容 -->
    <div class="tabs-content">
      <!-- A2D HD 4K 选项卡内容 -->
      <div v-if="activeTab === 'a2d_hd_4k'" class="tab-content">
        <!-- 主体内容 -->
        <div class="content-box">
          <!-- 左侧统计卡片区域 -->
          <div class="content-left">
            <div class="stats-container">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="iconfont icon-icon_devicePrint"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-label">{{ $t('agent.remainingAuth') }}</div>
                  <div class="stat-value">{{ currentTabData.remainingAuth }}</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="iconfont icon-payments"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-label">{{ $t('agent.totalPurchased') }}</div>
                  <div class="stat-value">{{ currentTabData.totalPurchased }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧表格区域 -->
          <div class="content-right">
            <div class="table-container">
              <!-- 使用 newTable 组件 -->
              <new-table
                class="recent-records-table"
                :data="displayRecords"
                :loading="false"
                :header-data="headerData"
                :hasIndex="false"
                max-height="200px"
              />
              <!-- 查看更多 -->
              <div v-if="showViewMore" class="table-more">
                <span class="show-more" @click="openDrawer">{{ $t('agent.viewMore') }} ></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预留其他选项卡内容 -->
      <!-- <div v-if="activeTab === 'design_service'" class="tab-content">
        <div class="coming-soon">
          <p>{{ $t('agent.designServiceComingSoon') }}</p>
        </div>
      </div>
      <div v-if="activeTab === 'ai_software'" class="tab-content">
        <div class="coming-soon">
          <p>{{ $t('agent.aiSoftwareComingSoon') }}</p>
        </div>
      </div> -->
    </div>

    <!-- 授权记录抽屉组件 -->
    <AgentAuthorizationRecord
      :show.sync="showAuthRecord"
      :agent-info="agentData"
      :device-type="activeTab"
      :auth-records="currentTabData.allRecords || []"
    />
  </div>
</template>

<script>
import AgentAuthorizationRecord from './AgentAuthorizationRecord.vue'
import newTable from '@/components/func-components/newTable.vue'

export default {
  name: 'AuthRecord',
  components: {
    AgentAuthorizationRecord,
    newTable
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    agentData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      agentInfo: {
        orgCode: '',
        agentName: '',
        agentCode: '',
        userCode: '',
        email: '',
        mobile: '',
        mobilePrefix: '',
        status: '',
        tzCode: ''
      },
      // 当前激活的选项卡
      activeTab: 'a2d_hd_4k',
      // 授权记录抽屉显示状态
      showAuthRecord: false,
      // 各选项卡的数据
      tabsData: {
        a2d_hd_4k: {
          remainingAuth: 20,
          totalPurchased: 100,
          // 最近3条记录（在主界面显示）
          recentRecords: [
            {
              operationTime: '2024-01-15 14:30:25',
              eventType: '授权',
              customerName: '客户名称A',
              deviceSN: 'SN A2D001234567',
              operator: '张三'
            },
            {
              operationTime: '2024-01-15 13:25:18',
              eventType: '授权',
              customerName: '客户名称B',
              deviceSN: 'SN A2D001234568',
              operator: '李四'
            },
            {
              operationTime: '2024-01-15 11:45:32',
              eventType: '撤销授权',
              customerName: '客户名称C',
              deviceSN: 'SN A2D001234569',
              operator: '王五'
            }
          ],
          // 完整记录（在抽屉中显示）
          allRecords: [
            {
              operationTime: '2024-01-15 14:30:25',
              eventType: '授权',
              customerName: '客户名称A',
              deviceSN: 'SN A2D001234567',
              operator: '张三'
            },
            {
              operationTime: '2024-01-15 13:25:18',
              eventType: '授权',
              customerName: '客户名称B',
              deviceSN: 'SN A2D001234568',
              operator: '李四'
            },
            {
              operationTime: '2024-01-15 11:45:32',
              eventType: '撤销授权',
              customerName: '客户名称C',
              deviceSN: 'SN A2D001234569',
              operator: '王五'
            },
            {
              operationTime: '2024-01-14 16:20:45',
              eventType: '授权',
              customerName: '客户名称D',
              deviceSN: 'SN A2D001234570',
              operator: '赵六'
            },
            {
              operationTime: '2024-01-14 10:15:30',
              eventType: '授权',
              customerName: '客户名称E',
              deviceSN: 'SN A2D001234571',
              operator: '系统'
            }
          ]
        }
        // 预留其他选项卡数据结构
        // design_service: { ... },
        // ai_software: { ... }
      }
    }
  },
  computed: {
    // 当前选项卡的数据
    currentTabData() {
      return this.tabsData[this.activeTab] || {}
    },
    // 表头数据配置
    headerData() {
      return [
        {
          prop: "operationTime",
          minWidth: "25%",
          noTip: false,
          getLabel: () => this.$t('agent.operationTime'),
        },
        {
          prop: "eventType",
          minWidth: "20%",
          noTip: false,
          getLabel: () => this.$t('agent.eventType'),
        },
        {
          prop: "customerName",
          minWidth: "25%",
          noTip: false,
          getLabel: () => this.$t('agent.endCustomer'),
        },
        {
          prop: "deviceSN",
          minWidth: "30%",
          noTip: false,
          getLabel: () => this.$t('agent.deviceSN'),
        }
      ];
    },
    // 显示的记录数据（最多3条）
    displayRecords() {
      const records = this.currentTabData.recentRecords || [];
      return records.slice(0, 3);
    },
    // 是否显示查看更多按钮
    showViewMore() {
      const records = this.currentTabData.recentRecords || [];
      return records.length > 3;
    }
  },
  watch: {
    show(val) {
      if (val && this.agentData) {
        // 从 props 接收数据，参考 EditAgent 的方式
        this.agentInfo = {
          orgCode: this.agentData.orgCode || '',
          agentName: this.agentData.orgName || this.agentData.agentName || '',
          agentCode: this.agentData.orgSn || this.agentData.agentCode || '',
          userCode: this.agentData.userCode || '',
          email: this.agentData.email || '',
          mobile: this.agentData.mobile || '',
          mobilePrefix: this.agentData.mobilePrefix || '+86',
          status: this.agentData.status || '',
          tzCode: this.agentData.tzCode || ''
        }

        console.log('代理商账户管理弹窗接收到的数据:', this.agentInfo)

        // 数据验证
        if (!this.agentInfo.orgCode) {
          console.warn('缺少必要的代理商组织编码')
          if (this.$MessageAlert) {
            this.$MessageAlert({
              text: this.$t('agent.missingOrgCode'),
              type: 'warning'
            })
          }
        }
      }
    }
  },
  methods: {
    // 关闭弹窗，参考 EditAgent 的方式
    goBack() {
      this.$emit('update:show', false);
    },
    // 切换选项卡
    changeTab(tabType) {
      this.activeTab = tabType;
      console.log('切换到选项卡:', tabType);
      // 切换选项卡时关闭授权记录抽屉
      this.showAuthRecord = false;
    },
    // 打开授权记录抽屉
    openDrawer() {
      this.showAuthRecord = true;
      console.log('打开授权记录抽屉');
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '1': this.$t('agent.statusNormal'),
        '0': this.$t('agent.statusPending'),
        '-1': this.$t('agent.statusRejected'),
        '2': this.$t('agent.statusDisabled')
      }
      return statusMap[status] || this.$t('agent.statusUnknown')
    }
  }
};
</script>

<style lang="scss" scoped>
.agent-account-box {
  z-index: 99;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  background: $hg-background-color;
  padding: 14px 24px 70px 24px;
  overflow-x: auto;
  overflow-y: hidden;

  .header {
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .back-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      height: $hg-height-32;
      color: $hg-secondary-fontcolor;
      i {
        margin-right: 8px;
      }
    }

    .header-title {
      color: $hg-primary-fontcolor;
      font-size: 16px;
      font-weight: bold;
      flex: 1;
      margin-left: 24px;

      em {
        color: $hg-main-blue;
        font-style: normal;
        margin-left: 8px;
      }
    }

    .header-actions {
      .action-btn {
        background-color: $hg-main-blue;
        border: none;
        color: $hg-primary-fontcolor;
        font-size: 12px;
        height: 32px;
        padding: 0 16px;
      }
    }
  }

  .header-tab {
    position: relative;
    height: 40px;
    margin-bottom: 24px;

    .tab-item {
      position: relative;
      display: inline-block;
      min-width: 104px;
      padding: 0 16px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: $hg-secondary-fontcolor;
      cursor: pointer;
      text-align: center;
      border-radius: 4px;
      margin-right: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: $hg-primary-fontcolor;
        background: $hg-hover-bg-color;
      }

      &.active {
        color: $hg-primary-fontcolor;
        background: $hg-main-blue;
      }
    }
  }

  .tabs-content {
    height: calc(100% - 64px);

    .tab-content {
      height: 100%;
    }

    .coming-soon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: $hg-secondary-fontcolor;
      font-size: 16px;
    }
  }

  .content-box {
    display: flex;
    height: 100%;
    gap: 24px;

    .content-left, .content-right {
      height: 50%;
      border-radius: 4px;
      background: $hg-main-black;
      box-shadow: 0px 12px 32px 0px $hg-background-color,0px 8px 24px 0px $hg-background-color,0px 0px 16px 0px $hg-background-color;
      padding: 16px 24px;

      .title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        height: 40px;

        .title-text {
          color: $hg-primary-fontcolor;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }

    .content-left {
      width: 412px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 8px 16px;
      gap: 8px;

      .stats-container {
        height: 248px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;

        .stat-card {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 8px 24px;
          gap: 16px;
          width: 380px;
          height: 112px;
          background: #27292E;
          border-radius: 8px;
          flex: none;

          .stat-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #2F3238;
            border-radius: 8px;
            flex: none;

            i {
              font-size: 24px;
              color: #F3F5F7;
            }
          }

          .stat-content {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0px;
            gap: 4px;
            flex: 1;

            .stat-label {
              color: #9EA2A8;
              font-family: 'Source Han Sans CN';
              font-style: normal;
              font-weight: 700;
              font-size: 14px;
              line-height: 20px;
              margin-bottom: 0;
              margin-right: 8px;
            }

            .stat-value {
              color: #F3F5F7;
              font-family: 'Oxygen';
              font-style: normal;
              font-weight: 700;
              font-size: 32px;
              line-height: 40px;
              display: flex;
              align-items: center;
              flex: none;
            }
          }
        }
      }
    }

    .content-right {
      flex: 1;

      .table-container {
        width: 100%;
        min-height: 248px;
        background: $hg-main-black;
        border-radius: 8px;
        padding: 16px;
        position: relative;
        display: flex;
        flex-direction: column;

        .recent-records-table {
          flex: none;

          ::v-deep .hg-table {
            .el-table {
              background: transparent;

              .el-table__header-wrapper {
                .header-row-item {
                  th {
                    background: transparent;
                    color: #C4C8CD;
                    border-bottom: 1px solid #38393D;
                    font-size: 12px;
                    height: 40px;
                    padding: 8px 12px;
                  }
                }
              }

              .el-table__body-wrapper {
                .row-item {
                  background: transparent;

                  &:hover {
                    background-color: #27292E;

                    td {
                      background-color: #27292E;
                    }
                  }

                  .cell-column-item {
                    color: #F3F5F7;
                    font-size: 14px;
                    height: 48px;
                    border-bottom: 1px dashed #2d2f33;
                    padding: 12px;
                  }
                }
              }
            }
          }
        }

        .table-more {
          position: absolute;
          bottom: 16px;
          right: 16px;

          .show-more {
            color: $hg-main-blue;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;

            &:hover {
              color: $hg-button-hover-fontcolor;
            }
          }
        }
      }
    }
  }
}
</style>
