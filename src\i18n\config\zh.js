/**
 * 中文文案，分模块备注
 */
module.exports = {
  // 个人中心模块
  personal: {
    back: '返回',
    uploadHeader: '上传头像',
    changeHeader: '更换头像',
    username: '账号',
    changeNamePlaceholder: '请输入账号',
    password: '密码',
    newPassword: '新密码',
    oldPassword: '旧密码',
    confrimPassword: '确认密码',
    passwordPh: '请输入密码',
    changeNewPwPh: '请输入新密码',
    changePwPh: '请输入当前密码',
    ensurePwPh: '请再次输入新密码',
    change: '修改',
    usernamePlaceholder: '请输入您的名称',
    name: '名称',
    phone: '手机号码',
    phonePlaceholder: '请输入您的手机号码',
    areaCode: '区号',
    cancle: '取消',
    save: '保存',
    confrim: '确认',
    email: '邮箱',
    emailPlaceholder: '请输入您的邮箱',
    uploadTip: '上传规范：',
    uploadTip1: '支持jpg、png、jpeg等格式的图片；',
    uploadTip2: '图片尺寸需大于100 * 100像素；',
    uploadTip3: '图片大小不能超过2MB；',
    phoneErro: '电话号码格式不正确',
    phonenull: '电话号码不能为空',
    emailErro: '邮箱格式不正确',
    realNameErro: '名称限50个字符',
    changePWSuccessTip: '修改密码成功！',
    saveInfoSuccessTip: '保存个人信息成功！',
    uploadFormatErro: '仅支持上传jpg、png、jpeg等格式的图片！',
    uploadSizeErro: '图片大小不能超过2MB！',
    uploadPXErro: '图片尺寸需大于100 * 100像素！',
    changePasswordTitle: '修改密码',
    passwordErro: '8~16个字符，大写字母、小写字母、数字、特殊字符至少三种组合',
    ensurePasswordErro: '两次输入的密码不相同',
    newPasswordErro: '请输入新密码',
    changeEmail: '修改邮箱',
    newEmail: '新邮箱',
    InputNewEmail: '请输入新的邮箱地址',
    emailCode: '验证码',
    InputEmailCode: '请输入校验码',
    sendCode: '发送验证码',
    emailError: '邮箱格式错误',
    emailExist: '该邮箱已被使用，请输入其他邮箱',
    codeError: '验证码错误',
    emailNull: '邮箱不能为空',
    codeNull: '验证码不能为空',
    changeEmailSuccess: '更新电子邮箱成功'
  },
  // 组织架构模块
  org: {
    areaCode: '区号',
    usernamePlaceholder: '请输入您的名称',
    name: '名称',
    email: '邮箱',
    emailPlaceholder: '请输入您的邮箱',
    phone: '手机号码',
    phonePlaceholder: '请输入您的手机号码',
    orgStructure: '组织架构',
    userList: '人员列表',
    searchTip: '请输入用户名称/手机号码/邮箱搜索',
    resetPassword: '重置密码',
    editUser: '编辑用户',
    deleteUser: '删除用户',
    userId: '用户ID',
    userName: '用户名称',
    role: '角色',
    department: '部门',
    phone: '电话',
    email: '邮箱',
    operate: '操作',
    addNewDept: '新增下级部门',
    addDeptSuccessTip: '添加部门成功！',
    editDeptNameSuccessTip: '编辑部门名称成功！',
    addUser: '新增用户',
    addUserSuccessTip: '添加用户成功，初始密码为：',
    copy: '复制',
    close: '关闭',
    deleteConfirm: '是否确定删除{orgText}【{orgName}】？',
    deleteBtn: '删除',
    organization: '组织',
    deleteDeptSuccessTip: '删除{orgText}成功！',
    editUserSuccessTip: '编辑用户成功！',
    resetPassword: '重置密码',
    confirm: '确认',
    resetPasswordConfirm: '请确认是否重置用户【{realName}】的密码？',
    resetSuccessTip: '重置成功，原始密码为：{password}',
    deleteUserConfirm: '是否确定删除用户【{realName}】？',
    deleteUserSuccessTip: '删除用户成功！',
    rolePlaceholder: '请选择角色',
    phoneErro: '电话号码格式不正确',
    emailErro: '邮箱格式不正确',
    realNameErro: '名称限50个字符',
    deptPlaceholder: '请选择部门',
    departmentName: '部门名称',
    deptNamePlaceholder: '请输入部门名称',
    parentDept: '上级部门',
    deptNameErro: '请输入1-20位英文、中文、数字或常用英文特殊字符'
  },
  // 客户管理模块
  customer: {
    title: '客户管理',
    areaCode: '区号',
    email: '邮箱',
    emailPlaceholder: '请输入负责人的邮箱',
    phone: '手机号码',
    phonePlaceholder: '请输入负责人的手机号码',
    save: '保存',
    password: '密码',
    searchPlaceholder: '请输入编码/客户名称搜索',
    addCustomerBtn: '添加客户',
    normal: '普通',
    directSales: '直营客户',
    undirectSales: '非直营客户',
    disaster: '灾备客户',
    testUser: '测试客户',
    approvalPending: '待审核',
    statusUnpass: '未通过',
    opened: '已通过',
    disable: '禁用',
    enable: '启用',
    disableText: '禁用',
    enableText: '启用',
    allStatus: '全部状态',
    orgCode: '客户编码',
    orgSn: '编码',
    orgName: '客户名称',
    customLevel: '客户级别',
    customType: '客户类型',
    createdTime: '创建时间',
    status: '状态',
    edit: '编辑',
    processSetting: '功能配置',
    customerOrg: '客户组织',
    refresh: '刷新',
    refreshSuccessTip: '刷新列表成功！',
    addCustomSuccessTip: '添加客户成功！',
    addCustomErrorTip: '添加客户失败！',
    editCustomSuccessTip: '编辑客户基础信息成功！',
    editCustomErrorTip: '编辑客户基础信息失败！',
    companyName: '公司名称',
    companyNamePlaceholder: '请填入您的公司名称',
    orgSnPlaceholder: '请填入编码',
    companyAddress: '公司地址',
    addressPlaceholder: '请填入您的公司地址',
    leader: '负责人',
    leaderPlaceholder: '请填入负责人',
    passwordPlaceholder: '请设置您的密码',
    phoneErro: '电话号码格式不正确',
    emailErro: '邮箱格式不正确',
    passwordErro: '请输入6-20位英文,数字和特殊符号的密码组合',
    goback: '返回',
    editCutomer: '客户编辑信息',
    regularInfo: '基础信息',
    customerNumber: '客户账号',
    number: '账号',
    reset: '重置',
    detail: '详细信息',
    resetPassword: '重置密码',
    confirm: '确认',
    resetPasswordConfirm: '请确认是否重置密码？',
    resetSuccessTip: '重置成功，原始密码为：{password}',
    copy: '复制',
    close: '关闭',
    editCustomerTitle: '编辑客户',
    operateConfirm: '请确认是否{operate}当前客户？',
    operateSuccessTip: '{operate}客户成功！',
    designService: '设计服务',
    import: '导入',
    searchSKUPlaceholder: '请输入SKU名称查询',
    byTimes: '按次',
    byMonth: '按月',
    byYear: '按年',
    forever: '永久',
    skuNumber: 'SKU编号',
    skuName: '名称',
    currency: '货币',
    standardPrice: '标准价格',
    salePrice: '销售价格',
    settleTimes: '结算周期',
    updateTime: '更新时间',
    relatePrice: '关联报价',
    open: '开通',
    skuConfirm: '是否确定{tipText}编号为【{skuCode}】的SKU？',
    operateSkuSuccessTip: '{tipText}成功！',
    uploadErro: '仅支持上传excel文件！',
    importSuccessTip: '导入成功！',
    orgSnErro: '编码不能超过15个字符，仅支持大小写英文、数字以及横杠（-）',
    s3Setting: 'S3配置',
    s3Placeholder: '请选择S3节点配置',
    settleTypes: '结算方式',
    creditValue: '信用值',
    creditPlaceholder: '请输入信用值',
    currencyType: '币种',
    currencyTypePlaceholder: '请选择币种',
    settleByMonth: '月结',
    settleByDeposit: '预充',
    discountRate: '折扣率',
    saleman: '业务人员',
    salemanPlaceholder: '请选择关联业务人员',
    pass: '通过',
    unpass: '不通过',
    examineTitle: '审核',
    examineTip: '请确认是否{isPass}当前客户的审核？',
    discountRateErro: '请输入0-100的整数',
    pretreatment: '前处理',
    statistics: '数据统计',
    functionSetting: '设计服务功能配置',
    priceSetting: '设计服务价格配置（单位：黑豆）',
    quickCreate: '极速建单',
    bindDesignGroup: '绑定设计组',
    customerName: '客户名称：',
    designType: '设计品类',
    unit: '单位',
    urgentTime: '{num}小时',
    urgentTime24: '24小时',
    quickSuccessTip: '{operate}极速建单模式成功！',
    openQuickOrderConfirm: '请确认是否{open}极速建单模式？',
    openQuickOrder: '开启',
    closeQuickOrder: '关闭',
    bindSuccessTip: '绑定设计组成功！',
    enterTip: '设计品类/物料编号',
    downloadPrice: '下载导入价格模版',
    importedTip: '全部文件{result}个，  成功导入xx个,失败xx个',
    application: '设计应用名称',
    materialID: '物料编号',
    importedStatus: '处理结果',
    No: '序号',
    importPrice: '导入价格',
    price: '单价',
    urgent: '交付时间',
    cancle: '取消',
    confrim: '确认',
    hours: '小时',
    searchCRM: '检索CRM',
    syncByCRM: 'CRM编码',
    syncSuccess: '已同步',
    unSync: '未同步',
    allCRMStatus: '全部',

    pay: '收款信息',
    bankName: '银行名称',
    bankLocation: '银行所在地',
    bankAdress: '银行地址',
    bankCode: '银行代码',
    bankAccouont: '账户名称',
    bankNumber: '账户编码',
    bankError: '请选择收款账户',
    remainding: '系统提示',
    businessTips: '业务人员为空',

    billFrom: '账单抬头',
    tel: '电话',
    mail: '邮箱',
    address: '地址',

    basicconfig: '基础配置',
    priceconfig: '价格配置',
    rush: '加急权限',
    uploadPrice: '导入价格',
    drag: '拖拽或点击上传价格表',
    uploadError: '请上传xlsx文件',
    serveice: '设计服务',
    exportClient: '导出客户',
    consume: '已使用',
    noConsume: '未使用',
    pleaseSelect: '请选择导出的客户',
    exporting: '正在导出，请稍后...',
    exportError: '导出失败',
    tableName: '客户信息表',
    notTax: '不含税',
    crm: '同步CRM',
    designApplication: '设计应用名称',
    artNo: '物料编码',
    unitPrice: '价格',
    asycBtn: '同步',
    crmPrice: 'CRM价格表',
    success: '成功',
    fail: '失败',
    konw: '知道了',
    boundsCrm: '客户未绑定CRM系统，请绑定后再同步。',
    obtainPrice: '获取CRM价格表失败。',
    includesTax: '确定标记为含税？',
    noIncludesTax: '确定标记为不含税？',
    syncFaild: '同步失败，请确保CRM与客户币种统一。',
    updatePrice: 'CRM 币种与当前币种不一致，是否进行币种更新？',
    updateBtn: '更新',

    categories: '不计数的设计品类',
    automatically: '自动剔除订单中不需要进行设计的设计品类。',
    categoryfilter: '设计品类过滤',
    designstatistics: '请选择不计数的设计品类',
    selected: '已选 {num} 个设计品类',

    bussinessSelect: '只能选择1个业务人员',
    area: '区域',
    areaPlaceholder: '请选择区域',
    agencyuser: '代理客户',

    memorySn: '助记码',
    memoryTips: '请输入助记码'
  },
  // 代理商管理模块
  agent: {
    title: '代理商管理',
    searchPlaceholder: '请输入编码/代理商名称搜索',
    addAgentBtn: '添加代理商',
    edit: '编辑',
    agentCode: '编码',
    agentName: '名称',
    agentScope: '代理范围',
    deviceCount: '终端客户数量',
    operation: '操作',
    refresh: '刷新',
    refreshSuccessTip: '刷新列表成功！',
    addAgentSuccessTip: '添加代理商成功！',
    addAgentErrorTip: '添加代理商失败！',
    editAgentSuccessTip: '编辑代理商成功！',
    editAgentErrorTip: '编辑代理商失败！',
    exportTransaction: '导出交易明细',
    noData: '暂无数据',
    manageAccount: '管理账户',
    viewDetail: '查看详情',
    getAgentInfoErrorTip: '获取代理商信息失败',
    missingOrgCode: '缺少代理商组织编号',
    missingCurrency: '缺少结算币种信息',
    getAgentListErrorTip: '获取代理商列表失败',
    noScopeSet: '未设置',
    device: '设备',
    designService: '设计服务',
    aiSoftware: 'AI软件',
    unknown: '未知',
    agentNamePlaceholder: '输入关键字从CRM已有客户中选择',
    agentNameRequired: '请输入代理商名称',
    agentCodePlaceholder: '请输入用户编码',
    agentCodeRequired: '请输入用户编码',
    agentScopePlaceholder: '请选择代理范围',
    agentScopeRequired: '请选择代理范围',
    agentScopeWarning: '提交后不允许修改已添加的代理范围',
    email: '邮箱',
    emailPlaceholder: '请输入用户邮箱',
    emailRequired: '请输入用户邮箱',
    emailFormatError: '邮箱格式错误',
    currency: '币种',
    currencyPlaceholder: '请选择代理商收益币种的所用币种',
    currencyRequired: '请选择币种',
    timezone: '时区',
    timezonePlaceholder: '请选择代理商所在时区',
    timezoneRequired: '请选择时区',
    address: '地址',
    addressPlaceholder: '请输入用户地址',
    leader: '负责人',
    leaderPlaceholder: '请输入负责人姓名',
    mobile: '联系电话',
    mobilePlaceholder: '请输入电话号码',
    mobileFormatError: '手机号格式错误',
    areaCode: '区号',
    businessUser: '业务员',
    businessUserPlaceholder: '请选择业务员',
    businessUserRequired: '请选择业务员',
    techSupport: '技术支持',
    techSupportPlaceholder: '请选择技术支持',
    techSupportRequired: '请选择技术支持',
    deviceAgent: '设备代理',
    designServiceAgent: '设计服务代理',
    aiSoftwareAgent: 'AI软件代理',
    editAgentTitle: '代理商信息编辑',
    basicInfo: '基础信息',
    searchCRM: '搜索CRM',
    memorySn: '助记码',
    account: '账号',
    password: '密码',
    status: '状态',
    enable: '启用',
    disable: '禁用',
    customerAccount: '客户账号',
    accountCode: '收款账户',
    accountCodePlaceholder: '请选择收款账户',
    accountCodeRequired: '请选择收款账户',
    headerCode: '账单抬头',
    headerCodePlaceholder: '请选择账单抬头',
    headerCodeRequired: '请选择账单抬头',
    bankName: '银行名称',
    bankLocation: '银行所在地',
    bankAdress: '银行地址',
    bankCode: '银行代码',
    bankAccouont: '账户名称',
    bankNumber: '账户账号',
    giveUpEditTip: '将放弃未提交的修改',
    businessUserOnlyOne: '业务人员不能超过一个',
    resetPasswordConfirm: '确认重置密码？',
    resetPasswordAgent: '代理商',
    resetPasswordSuccess: '密码重置成功',
    resetPasswordAfter: '密码重置后：',
    unnamedAgent: '未命名代理商',
    examineTitle: '审核',
    examineConfirm: '确认{isPass}审核？',
    confirmChangeStatus: '确认{actionText}代理商？',
    updateCurrencyTip: '是否更新币种?',
    reminder: '提醒',
    update: '更新',
    pass: '通过',
    unpass: '不通过',
    customerList: '客户列表',
    customerName: '客户名称',
    customerNamePlaceholder: '请输入客户名称',
    getCustomerListErrorTip: '获取客户列表失败',
    // 代理商账户管理页面
    accountTitle: '账户',
    back: '返回',
    remainingAuth: '剩余授权数',
    totalPurchased: '总购买数',
    operationTime: '操作时间',
    eventType: '事件类型',
    endCustomer: '终端客户',
    deviceSN: '设备SN',
    viewMore: '查看更多',
    authorizationRecord: '授权记录',
    authorization: '授权',
    revokeAuthorization: '撤销授权',
    startDate: '开始日期',
    endDate: '结束日期',
    deviceModel: '机型',
    allOptions: '全部',
    export: '导出',
    newAddedCount: '新添加数量',
    operator: '操作人',
    system: '系统',
    exportInProgress: '导出功能开发中...',
    // 状态相关
    statusNormal: '正常',
    statusPending: '待审核',
    statusRejected: '审核不通过',
    statusDisabled: '禁用',
    statusUnknown: '未知状态',
    // 功能即将开放
    comingSoon: '功能即将开放',
    designServiceComingSoon: '设计服务功能即将开放',
    aiSoftwareComingSoon: 'AI软件功能即将开放'
  },
  // 头部模块
  header: {
    center: '个人中心',
    enterprise: '企业信息',
    framework: '组织架构',
    language: '切换语言',
    out: '退出登录',
    chinese: '中文',
    english: '英文'
  },

  // 公共模块
  common: {
    loading: '加载中...',
    noData: '暂无数据',
    cancel: '取消',
    submit: '提交',
    confirm: '确定',
    systemTip: '系统提示',
    skipTo: '跳至',
    page: '页',
    searchTip: '请输入关键字',
    selectPlaceholder: '请选择',
    goback: '返回',
    copySuccessTip: '复制成功！',
    loginFail: '登录失败',
    loginByOthers: '异地登录，请重新登录！（{erroCode}）',
    loginTimeOut: '登录过期',
    loginTimeOutTip: '登录信息过期，请重新登录！',
    loginOutTip: '请重新登录！（{erroCode}）',
    networkBusy: '访问异常！（{erroCode}）',
    authError: '无权限访问！（{erroCode}）',
    // networkBusy: '网络繁忙，请重试！（{erroCode}）',
    saveTip: '提示',
    saveTipContext: '当前页面修改尚未保存，是否确认离开本页面，离开将放弃这些修改',
    save: '保存',
    reset: '重置',
    confirmReturn: '确认返回',
    operateSuccessTip: '操作成功',
    success: '成功',
    close: '关闭',
    copy: '复制'
  },

  // 时区
  timezone: {
    timezone: '时区',
    CN: '上海(UTC{utc})',
    JP: '东京(UTC{utc})',
    US: '纽约(UTC{utc})',
    GB: '伦敦(UTC{utc})',
    RU: '莫斯科(UTC{utc})',
    AU: '悉尼(UTC{utc})',
    CA: '温哥华(UTC{utc})',
    NE: '尼亚美(UTC{utc})',
    EG: '开罗(UTC{utc})',
    IN: '加尔各答(UTC{utc})',
    BR: '圣保罗(UTC{utc})',
    FR: '巴黎(UTC{utc})',
    DE: '柏林(UTC{utc})',
    ES: '马德里(UTC{utc})',
    PL: '华沙(UTC{utc})',
    IT: '罗马(UTC{utc})',
    LT: '维尔纽斯(UTC{utc})',
    BE: '布鲁塞尔(UTC{utc})',
    SL: '弗里敦(UTC{utc})',
    timezoneErr: '请选择时区'
  },

  // lab租户端角色
  50015: '管理员',
  50016: '技工',
  50017: '运营',
  50018: '系统管理员',
  50019: '系统运营',
  50020: '设计运营',
  50021: '设计师',
  50022: '技术支持',
  50023: '财务',
  50024: '医助',
  50031: '设计师组长',
  50032: '业务人员',
  50033: 'IQC',
  50034: 'OQC',
  50035: '设备出厂工程师',
  50039: '知识库管理员',

  // 错误码对应的相关提示
  65000001: '已经存在相同的登录账号',
  65000002: '用户名或密码错误！',
  65000003: '找不到该帐号！',
  65000050: '组织（客户、部门）已存在！',
  65000051: '组织（客户、部门）不存在！',
  65000053: '部门不可删除，请先将下级部门或部门下的人员移除！',
  65000055: '禁止编辑客户名称！',
  60020001: '方案不存在！',
  60020002: '设计类型不存在！',
  60020003: '编码为{message}的SKU不存在！',
  60020004: 'SKU未开通！',
  60020005: '基础设计参数未配置！',
  60020006: '基础信息通知参数未配置！',
  60020100: '用户数据获取失败',
  60027000: '文件不存在！',
  60027001: '非excel文件！',
  60027002: '表格数据不能为空！',
  60027003: 'excel解析失败！',
  60027004: '编码为{message}的SKU数据格式有误！',
  60027005: '编码为{message}的SKU货币种类不存在！',
  60027006: 'SKU模板或填写错误！',
  60027007: 'SKU编码填写重复！',
  60027008: '编码为{message}的SKU折后价为空！',
  60027009: '编码为{message}的SKU价格为空！',
  65000056: '客户编码已存在！',
  70100005: '异地登录，请重新登录！',
  65000009: '手机号码已存在！',
  // 信用值
  11010032: '信用值最大值不能超过 {message}',
  65000014: '新密码不能与旧密码一致',
  65000015: '旧密码错误',
  // 70100011, // 该账号不存在
  // 70100012, // 密码错误
  // 70100015, // 账号被禁用
  'syncFail': '同步CRM失败',
  'syncFail2': '操作失败，该用户CRM已同步',
  '': '',
}
